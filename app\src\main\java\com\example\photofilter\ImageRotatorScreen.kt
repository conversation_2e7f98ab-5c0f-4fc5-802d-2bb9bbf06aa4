package com.example.photofilter

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Checkbox
import androidx.compose.material.CheckboxDefaults
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Slider
import androidx.compose.material.SliderDefaults
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Brightness4
import androidx.compose.material.icons.filled.Brightness6
import androidx.compose.material.icons.filled.Brightness7
import androidx.compose.material.icons.filled.ColorLens
import androidx.compose.material.icons.filled.Contrast
import androidx.compose.material.icons.filled.FilterBAndW
import androidx.compose.material.icons.filled.FilterVintage
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.InvertColors
import androidx.compose.material.icons.filled.Palette
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.RotateRight
import androidx.compose.material.icons.filled.SwapHoriz
import androidx.compose.material.icons.filled.SwapVert
import androidx.compose.material.icons.filled.Tune
import androidx.compose.material.icons.filled.WbSunny
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myapp.ui.theme.PhotoFilterTheme
import com.example.photofilter.processor.ImageProcessor

@Composable
fun ImageRotatorScreen(
    imageProcessor: ImageProcessor,
    onSelectImageClick: () -> Unit,
    onRotateImageClick: () -> Unit,
    onFlipHorizontalClick: () -> Unit,
    onFlipVerticalClick: () -> Unit,
    onResetClick: () -> Unit,
    // Color filter callbacks
    onGrayscaleClick: () -> Unit,
    onSepiaClick: () -> Unit,
    onIncreaseBrightnessClick: () -> Unit,
    onDecreaseBrightnessClick: () -> Unit,
    onIncreaseContrastClick: () -> Unit,
    onDecreaseContrastClick: () -> Unit,
    onIncreaseSaturationClick: () -> Unit,
    onDecreaseSaturationClick: () -> Unit,
    onCoolFilterClick: () -> Unit,
    onWarmFilterClick: () -> Unit,
    onVintageFilterClick: () -> Unit,
    onInvertColorsClick: () -> Unit,
    onRotationSliderChange: (Float) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Tiêu đề
        Text(
            text = "Ứng dụng Tải và Xoay Ảnh",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colors.primary,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 20.dp)
        )

        // Nút chọn ảnh
        Button(
            onClick = onSelectImageClick,
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 12.dp),
            colors = ButtonDefaults.buttonColors(
                backgroundColor = MaterialTheme.colors.primary
            )
        ) {
            Icon(
                imageVector = Icons.Default.Image,
                contentDescription = null,
                modifier = Modifier.padding(end = 8.dp)
            )
            Text("Chọn ảnh")
        }

        // Các nút điều khiển - Hàng 1: Xoay và Lật ngang
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Nút xoay ảnh
            Button(
                onClick = onRotateImageClick,
                modifier = Modifier.weight(1f),
                enabled = imageProcessor.hasImage(),
                colors = ButtonDefaults.buttonColors(
                    backgroundColor = MaterialTheme.colors.secondary
                )
            ) {
                Icon(
                    imageVector = Icons.Default.RotateRight,
                    contentDescription = null,
                    modifier = Modifier.padding(end = 4.dp)
                )
                Text("Xoay")
            }

            // Nút lật ngang
            Button(
                onClick = onFlipHorizontalClick,
                modifier = Modifier.weight(1f),
                enabled = imageProcessor.hasImage(),
                colors = ButtonDefaults.buttonColors(
                    backgroundColor = MaterialTheme.colors.secondary
                )
            ) {
                Icon(
                    imageVector = Icons.Default.SwapHoriz,
                    contentDescription = null,
                    modifier = Modifier.padding(end = 4.dp)
                )
                Text("Lật ngang")
            }
        }

        // Các nút điều khiển - Hàng 2: Lật dọc
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 12.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Nút lật dọc
            Button(
                onClick = onFlipVerticalClick,
                modifier = Modifier.weight(1f),
                enabled = imageProcessor.hasImage(),
                colors = ButtonDefaults.buttonColors(
                    backgroundColor = MaterialTheme.colors.secondary
                )
            ) {
                Icon(
                    imageVector = Icons.Default.SwapVert,
                    contentDescription = null,
                    modifier = Modifier.padding(end = 4.dp)
                )
                Text("Lật dọc")
            }

            // Nút reset (chiếm nửa còn lại)
            Button(
                onClick = onResetClick,
                modifier = Modifier.weight(1f),
                enabled = imageProcessor.hasImage(),
                colors = ButtonDefaults.buttonColors(
                    backgroundColor = MaterialTheme.colors.error
                )
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = null,
                    modifier = Modifier.padding(end = 4.dp)
                )
                Text("Reset")
            }
        }

        // Khu vực hiển thị ảnh
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .height(400.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colors.surface
            )
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                if (imageProcessor.currentBitmap != null) {
                    Image(
                        bitmap = imageProcessor.currentBitmap!!.asImageBitmap(),
                        contentDescription = "Selected image",
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(8.dp)
                            .clip(RoundedCornerShape(8.dp)),
                        contentScale = ContentScale.Fit
                    )
                } else {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Image,
                            contentDescription = null,
                            modifier = Modifier.size(64.dp),
                            tint = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                        )
                        Text(
                            text = "Chưa có ảnh nào",
                            color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                            modifier = Modifier.padding(top = 8.dp)
                        )
                    }
                }
            }
        }

        // Color Filters Section
        if (imageProcessor.hasImage()) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Xoay ảnh: ${imageProcessor.sliderRotationValue.toInt()}° (Phóng to vừa khung)",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colors.primary
                )

                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = imageProcessor.autoScaleToFit,
                        onCheckedChange = { },
                        colors = CheckboxDefaults.colors(
                            checkedColor = MaterialTheme.colors.primary
                        )
                    )
                    Text(
                        text = "Vừa khung",
                        fontSize = 12.sp,
                        color = MaterialTheme.colors.onSurface
                    )
                }
            }

            Slider(
                value = imageProcessor.sliderRotationValue,
                onValueChange = { value -> onRotationSliderChange(value) },
                valueRange = 0f..360f,
                steps = 71, // Mỗi bước 5 độ
                colors = SliderDefaults.colors(
                    thumbColor = MaterialTheme.colors.primary,
                    activeTrackColor = MaterialTheme.colors.primary
                )
            )

            Text(
                text = "Bộ lọc màu sắc",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colors.primary,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(top = 20.dp, bottom = 12.dp)
            )

            // Basic Filters Row 1
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 8.dp),
                horizontalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                ColorFilterButton(
                    onClick = onGrayscaleClick,
                    icon = Icons.Default.FilterBAndW,
                    text = "Đen trắng",
                    modifier = Modifier.weight(1f)
                )
                ColorFilterButton(
                    onClick = onSepiaClick,
                    icon = Icons.Default.FilterVintage,
                    text = "Sepia",
                    modifier = Modifier.weight(1f)
                )
                ColorFilterButton(
                    onClick = onInvertColorsClick,
                    icon = Icons.Default.InvertColors,
                    text = "Đảo màu",
                    modifier = Modifier.weight(1f)
                )
            }

            // Brightness Controls Row 2
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 8.dp),
                horizontalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                ColorFilterButton(
                    onClick = onIncreaseBrightnessClick,
                    icon = Icons.Default.Brightness7,
                    text = "Sáng +",
                    modifier = Modifier.weight(1f)
                )
                ColorFilterButton(
                    onClick = onDecreaseBrightnessClick,
                    icon = Icons.Default.Brightness4,
                    text = "Sáng -",
                    modifier = Modifier.weight(1f)
                )
                ColorFilterButton(
                    onClick = onIncreaseContrastClick,
                    icon = Icons.Default.Contrast,
                    text = "Tương phản +",
                    modifier = Modifier.weight(1f)
                )
            }

            // Saturation & Contrast Row 3
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 8.dp),
                horizontalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                ColorFilterButton(
                    onClick = onDecreaseContrastClick,
                    icon = Icons.Default.Tune,
                    text = "Tương phản -",
                    modifier = Modifier.weight(1f)
                )
                ColorFilterButton(
                    onClick = onIncreaseSaturationClick,
                    icon = Icons.Default.Palette,
                    text = "Bão hòa +",
                    modifier = Modifier.weight(1f)
                )
                ColorFilterButton(
                    onClick = onDecreaseSaturationClick,
                    icon = Icons.Default.ColorLens,
                    text = "Bão hòa -",
                    modifier = Modifier.weight(1f)
                )
            }

            // Temperature Filters Row 4
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp),
                horizontalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                ColorFilterButton(
                    onClick = onCoolFilterClick,
                    icon = Icons.Default.Brightness6,
                    text = "Màu lạnh",
                    modifier = Modifier.weight(1f)
                )
                ColorFilterButton(
                    onClick = onWarmFilterClick,
                    icon = Icons.Default.WbSunny,
                    text = "Màu ấm",
                    modifier = Modifier.weight(1f)
                )
                ColorFilterButton(
                    onClick = onVintageFilterClick,
                    icon = Icons.Default.FilterVintage,
                    text = "Vintage",
                    modifier = Modifier.weight(1f)
                )
            }
        }

        // Thông tin trạng thái
        Column(
            modifier = Modifier.padding(top = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = imageProcessor.statusText,
                fontSize = 16.sp,
                color = MaterialTheme.colors.onSurface,
                textAlign = TextAlign.Center
            )

            // Hiển thị thông tin ảnh nếu có
            imageProcessor.getImageInfo()?.let { info ->
                Text(
                    text = info,
                    fontSize = 14.sp,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
        }
    }
}

@Composable
fun ColorFilterButton(
    onClick: () -> Unit,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    text: String,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = onClick,
        modifier = modifier.height(48.dp),
        colors = ButtonDefaults.buttonColors(
            backgroundColor = MaterialTheme.colors.surface,
            contentColor = MaterialTheme.colors.onSurface
        ),
        elevation = ButtonDefaults.elevation(defaultElevation = 2.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
            Text(
                text = text,
                fontSize = 10.sp,
                textAlign = TextAlign.Center,
                maxLines = 1
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ImageRotatorScreenPreview() {
    PhotoFilterTheme {
        ImageRotatorScreen(
            imageProcessor = ImageProcessor(),
            onSelectImageClick = { },
            onRotateImageClick = { },
            onFlipHorizontalClick = { },
            onFlipVerticalClick = { },
            onResetClick = { },
            onGrayscaleClick = { },
            onSepiaClick = { },
            onIncreaseBrightnessClick = { },
            onDecreaseBrightnessClick = { },
            onIncreaseContrastClick = { },
            onDecreaseContrastClick = { },
            onIncreaseSaturationClick = { },
            onDecreaseSaturationClick = { },
            onCoolFilterClick = { },
            onWarmFilterClick = { },
            onVintageFilterClick = { },
            onInvertColorsClick = { },
            onRotationSliderChange = {},
        )
    }
}package com.example.photofilter

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Checkbox
import androidx.compose.material.CheckboxDefaults
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Slider
import androidx.compose.material.SliderDefaults
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Brightness4
import androidx.compose.material.icons.filled.Brightness6
import androidx.compose.material.icons.filled.Brightness7
import androidx.compose.material.icons.filled.ColorLens
import androidx.compose.material.icons.filled.Contrast
import androidx.compose.material.icons.filled.FilterBAndW
import androidx.compose.material.icons.filled.FilterVintage
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.InvertColors
import androidx.compose.material.icons.filled.Palette
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.RotateRight
import androidx.compose.material.icons.filled.SwapHoriz
import androidx.compose.material.icons.filled.SwapVert
import androidx.compose.material.icons.filled.Tune
import androidx.compose.material.icons.filled.WbSunny
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.myapp.ui.theme.PhotoFilterTheme
import com.example.photofilter.processor.ImageProcessor

@Composable
fun ImageRotatorScreen(
    imageProcessor: ImageProcessor,
    onSelectImageClick: () -> Unit,
    onRotateImageClick: () -> Unit,
    onFlipHorizontalClick: () -> Unit,
    onFlipVerticalClick: () -> Unit,
    onResetClick: () -> Unit,
    // Color filter callbacks
    onGrayscaleClick: () -> Unit,
    onSepiaClick: () -> Unit,
    onIncreaseBrightnessClick: () -> Unit,
    onDecreaseBrightnessClick: () -> Unit,
    onIncreaseContrastClick: () -> Unit,
    onDecreaseContrastClick: () -> Unit,
    onIncreaseSaturationClick: () -> Unit,
    onDecreaseSaturationClick: () -> Unit,
    onCoolFilterClick: () -> Unit,
    onWarmFilterClick: () -> Unit,
    onVintageFilterClick: () -> Unit,
    onInvertColorsClick: () -> Unit,
    onRotationSliderChange: (Float) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Tiêu đề
        Text(
            text = "Ứng dụng Tải và Xoay Ảnh",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colors.primary,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 20.dp)
        )

        // Nút chọn ảnh
        Button(
            onClick = onSelectImageClick,
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 12.dp),
            colors = ButtonDefaults.buttonColors(
                backgroundColor = MaterialTheme.colors.primary
            )
        ) {
            Icon(
                imageVector = Icons.Default.Image,
                contentDescription = null,
                modifier = Modifier.padding(end = 8.dp)
            )
            Text("Chọn ảnh")
        }

        // Các nút điều khiển - Hàng 1: Xoay và Lật ngang
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Nút xoay ảnh
            Button(
                onClick = onRotateImageClick,
                modifier = Modifier.weight(1f),
                enabled = imageProcessor.hasImage(),
                colors = ButtonDefaults.buttonColors(
                    backgroundColor = MaterialTheme.colors.secondary
                )
            ) {
                Icon(
                    imageVector = Icons.Default.RotateRight,
                    contentDescription = null,
                    modifier = Modifier.padding(end = 4.dp)
                )
                Text("Xoay")
            }

            // Nút lật ngang
            Button(
                onClick = onFlipHorizontalClick,
                modifier = Modifier.weight(1f),
                enabled = imageProcessor.hasImage(),
                colors = ButtonDefaults.buttonColors(
                    backgroundColor = MaterialTheme.colors.secondary
                )
            ) {
                Icon(
                    imageVector = Icons.Default.SwapHoriz,
                    contentDescription = null,
                    modifier = Modifier.padding(end = 4.dp)
                )
                Text("Lật ngang")
            }
        }

        // Các nút điều khiển - Hàng 2: Lật dọc
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 12.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Nút lật dọc
            Button(
                onClick = onFlipVerticalClick,
                modifier = Modifier.weight(1f),
                enabled = imageProcessor.hasImage(),
                colors = ButtonDefaults.buttonColors(
                    backgroundColor = MaterialTheme.colors.secondary
                )
            ) {
                Icon(
                    imageVector = Icons.Default.SwapVert,
                    contentDescription = null,
                    modifier = Modifier.padding(end = 4.dp)
                )
                Text("Lật dọc")
            }

            // Nút reset (chiếm nửa còn lại)
            Button(
                onClick = onResetClick,
                modifier = Modifier.weight(1f),
                enabled = imageProcessor.hasImage(),
                colors = ButtonDefaults.buttonColors(
                    backgroundColor = MaterialTheme.colors.error
                )
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = null,
                    modifier = Modifier.padding(end = 4.dp)
                )
                Text("Reset")
            }
        }

        // Khu vực hiển thị ảnh
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .height(400.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colors.surface
            )
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                if (imageProcessor.currentBitmap != null) {
                    Image(
                        bitmap = imageProcessor.currentBitmap!!.asImageBitmap(),
                        contentDescription = "Selected image",
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(8.dp)
                            .clip(RoundedCornerShape(8.dp)),
                        contentScale = ContentScale.Fit
                    )
                } else {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Image,
                            contentDescription = null,
                            modifier = Modifier.size(64.dp),
                            tint = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                        )
                        Text(
                            text = "Chưa có ảnh nào",
                            color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                            modifier = Modifier.padding(top = 8.dp)
                        )
                    }
                }
            }
        }

        // Color Filters Section
        if (imageProcessor.hasImage()) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Xoay ảnh: ${imageProcessor.sliderRotationValue.toInt()}° (Phóng to vừa khung)",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colors.primary
                )

                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = imageProcessor.autoScaleToFit,
                        onCheckedChange = { },
                        colors = CheckboxDefaults.colors(
                            checkedColor = MaterialTheme.colors.primary
                        )
                    )
                    Text(
                        text = "Vừa khung",
                        fontSize = 12.sp,
                        color = MaterialTheme.colors.onSurface
                    )
                }
            }

            Slider(
                value = imageProcessor.sliderRotationValue,
                onValueChange = { value -> onRotationSliderChange(value) },
                valueRange = 0f..360f,
                steps = 71, // Mỗi bước 5 độ
                colors = SliderDefaults.colors(
                    thumbColor = MaterialTheme.colors.primary,
                    activeTrackColor = MaterialTheme.colors.primary
                )
            )

            Text(
                text = "Bộ lọc màu sắc",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colors.primary,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(top = 20.dp, bottom = 12.dp)
            )

            // Basic Filters Row 1
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 8.dp),
                horizontalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                ColorFilterButton(
                    onClick = onGrayscaleClick,
                    icon = Icons.Default.FilterBAndW,
                    text = "Đen trắng",
                    modifier = Modifier.weight(1f)
                )
                ColorFilterButton(
                    onClick = onSepiaClick,
                    icon = Icons.Default.FilterVintage,
                    text = "Sepia",
                    modifier = Modifier.weight(1f)
                )
                ColorFilterButton(
                    onClick = onInvertColorsClick,
                    icon = Icons.Default.InvertColors,
                    text = "Đảo màu",
                    modifier = Modifier.weight(1f)
                )
            }

            // Brightness Controls Row 2
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 8.dp),
                horizontalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                ColorFilterButton(
                    onClick = onIncreaseBrightnessClick,
                    icon = Icons.Default.Brightness7,
                    text = "Sáng +",
                    modifier = Modifier.weight(1f)
                )
                ColorFilterButton(
                    onClick = onDecreaseBrightnessClick,
                    icon = Icons.Default.Brightness4,
                    text = "Sáng -",
                    modifier = Modifier.weight(1f)
                )
                ColorFilterButton(
                    onClick = onIncreaseContrastClick,
                    icon = Icons.Default.Contrast,
                    text = "Tương phản +",
                    modifier = Modifier.weight(1f)
                )
            }

            // Saturation & Contrast Row 3
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 8.dp),
                horizontalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                ColorFilterButton(
                    onClick = onDecreaseContrastClick,
                    icon = Icons.Default.Tune,
                    text = "Tương phản -",
                    modifier = Modifier.weight(1f)
                )
                ColorFilterButton(
                    onClick = onIncreaseSaturationClick,
                    icon = Icons.Default.Palette,
                    text = "Bão hòa +",
                    modifier = Modifier.weight(1f)
                )
                ColorFilterButton(
                    onClick = onDecreaseSaturationClick,
                    icon = Icons.Default.ColorLens,
                    text = "Bão hòa -",
                    modifier = Modifier.weight(1f)
                )
            }

            // Temperature Filters Row 4
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp),
                horizontalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                ColorFilterButton(
                    onClick = onCoolFilterClick,
                    icon = Icons.Default.Brightness6,
                    text = "Màu lạnh",
                    modifier = Modifier.weight(1f)
                )
                ColorFilterButton(
                    onClick = onWarmFilterClick,
                    icon = Icons.Default.WbSunny,
                    text = "Màu ấm",
                    modifier = Modifier.weight(1f)
                )
                ColorFilterButton(
                    onClick = onVintageFilterClick,
                    icon = Icons.Default.FilterVintage,
                    text = "Vintage",
                    modifier = Modifier.weight(1f)
                )
            }
        }

        // Thông tin trạng thái
        Column(
            modifier = Modifier.padding(top = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = imageProcessor.statusText,
                fontSize = 16.sp,
                color = MaterialTheme.colors.onSurface,
                textAlign = TextAlign.Center
            )

            // Hiển thị thông tin ảnh nếu có
            imageProcessor.getImageInfo()?.let { info ->
                Text(
                    text = info,
                    fontSize = 14.sp,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
        }
    }
}

@Composable
fun ColorFilterButton(
    onClick: () -> Unit,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    text: String,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = onClick,
        modifier = modifier.height(48.dp),
        colors = ButtonDefaults.buttonColors(
            backgroundColor = MaterialTheme.colors.surface,
            contentColor = MaterialTheme.colors.onSurface
        ),
        elevation = ButtonDefaults.elevation(defaultElevation = 2.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
            Text(
                text = text,
                fontSize = 10.sp,
                textAlign = TextAlign.Center,
                maxLines = 1
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ImageRotatorScreenPreview() {
    PhotoFilterTheme {
        ImageRotatorScreen(
            imageProcessor = ImageProcessor(),
            onSelectImageClick = { },
            onRotateImageClick = { },
            onFlipHorizontalClick = { },
            onFlipVerticalClick = { },
            onResetClick = { },
            onGrayscaleClick = { },
            onSepiaClick = { },
            onIncreaseBrightnessClick = { },
            onDecreaseBrightnessClick = { },
            onIncreaseContrastClick = { },
            onDecreaseContrastClick = { },
            onIncreaseSaturationClick = { },
            onDecreaseSaturationClick = { },
            onCoolFilterClick = { },
            onWarmFilterClick = { },
            onVintageFilterClick = { },
            onInvertColorsClick = { },
            onRotationSliderChange = {},
        )
    }
}