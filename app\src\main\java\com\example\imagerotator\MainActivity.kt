package com.example.imagerotator

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.example.imagerotator.databinding.ActivityMainBinding
import java.io.IOException

class MainActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMainBinding
    private var currentBitmap: Bitmap? = null
    private var rotationAngle = 0f
    
    // Activity result launcher để chọn ảnh từ gallery
    private val selectImageLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            result.data?.data?.let { uri ->
                loadImageFromUri(uri)
            }
        }
    }
    
    // Permission launcher để xin quyền truy cập storage
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            openImagePicker()
        } else {
            Toast.makeText(this, "Cần quyền truy cập để chọn ảnh", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupClickListeners()
    }
    
    private fun setupClickListeners() {
        // Nút chọn ảnh
        binding.btnSelectImage.setOnClickListener {
            checkPermissionAndSelectImage()
        }
        
        // Nút xoay ảnh
        binding.btnRotateImage.setOnClickListener {
            rotateImage()
        }
    }
    
    private fun checkPermissionAndSelectImage() {
        when {
            ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED -> {
                openImagePicker()
            }
            else -> {
                requestPermissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
        }
    }
    
    private fun openImagePicker() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        intent.type = "image/*"
        selectImageLauncher.launch(intent)
    }
    
    private fun loadImageFromUri(uri: Uri) {
        try {
            val inputStream = contentResolver.openInputStream(uri)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()
            
            if (bitmap != null) {
                currentBitmap = bitmap
                rotationAngle = 0f
                binding.imageView.setImageBitmap(bitmap)
                binding.btnRotateImage.isEnabled = true
                binding.tvImageStatus.text = "Đã tải ảnh thành công"
            } else {
                Toast.makeText(this, "Không thể tải ảnh", Toast.LENGTH_SHORT).show()
            }
        } catch (e: IOException) {
            Toast.makeText(this, "Lỗi khi tải ảnh: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun rotateImage() {
        currentBitmap?.let { bitmap ->
            rotationAngle += 90f
            if (rotationAngle >= 360f) {
                rotationAngle = 0f
            }
            
            val matrix = Matrix()
            matrix.postRotate(rotationAngle)
            
            val rotatedBitmap = Bitmap.createBitmap(
                bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true
            )
            
            binding.imageView.setImageBitmap(rotatedBitmap)
            binding.tvImageStatus.text = "Đã xoay ảnh ${rotationAngle.toInt()}°"
        }
    }
}
