 Caused by: java.lang.ClassNotFoundException: Didn't find class "com.example.photofilter.MainActivity" on path: DexPathList[[zip file "/data/app/com.example.photofilter-rxBhrwfAIGTkCcRwpWl3EQ==/base.apk"],nativeLibraryDirectories=[/data/app/com.example.photofilter-rxBhrwfAIGTkCcRwpWl3EQ==/lib/x86, /data/app/com.example.photofilter-rxBhrwfAIGTkCcRwpWl3EQ==/base.apk!/lib/x86, /system/lib]]
                                                                                                    	at dalvik.system.BaseDexClassLoader.findClass(BaseDexClassLoader.java:134)
                                                                                                    	at java.lang.ClassLoader.loadClass(ClassLoader.java:379)
                                                                                                    	at java.lang.ClassLoader.loadClass(ClassLoader.java:312)
                                                                                                    	at android.app.AppComponentFactory.instantiateActivity(AppComponentFactory.java:69)
                                                                                                    	at androidx.c