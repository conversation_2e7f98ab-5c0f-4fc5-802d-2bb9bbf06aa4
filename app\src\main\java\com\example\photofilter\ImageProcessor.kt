package com.example.photofilter

import android.content.ContentResolver
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.Matrix
import android.graphics.Paint
import android.net.Uri
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import java.io.IOException
import kotlin.math.max
import kotlin.math.min

class ImageProcessor {
    
    // State variables
    var currentBitmap by mutableStateOf<Bitmap?>(null)
        private set
    
    var rotationAngle by mutableStateOf(0f)
        private set
    
    var statusText by mutableStateOf("Chưa chọn ảnh nào")
        private set
    
    /**
     * Tải ảnh từ URI
     * @param uri URI của ảnh cần tải
     * @param contentResolver ContentResolver để đọc file
     * @return true nếu tải thành công, false nếu thất bại
     */
    fun loadImageFromUri(uri: Uri, contentResolver: ContentResolver): <PERSON><PERSON>an {
        return try {
            val inputStream = contentResolver.openInputStream(uri)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()
            
            if (bitmap != null) {
                currentBitmap = bitmap
                rotationAngle = 0f
                statusText = "Đã tải ảnh thành công"
                true
            } else {
                statusText = "Không thể tải ảnh"
                false
            }
        } catch (e: IOException) {
            statusText = "Lỗi khi tải ảnh: ${e.message}"
            false
        }
    }
    
    /**
     * Xoay ảnh hiện tại 90 độ
     * @return true nếu xoay thành công, false nếu không có ảnh để xoay
     */
    fun rotateImage(): Boolean {
        val bitmap = currentBitmap ?: return false

        rotationAngle += 90f
        if (rotationAngle >= 360f) {
            rotationAngle = 0f
        }

        val matrix = Matrix()
        matrix.postRotate(90f) // Luôn xoay 90 độ mỗi lần

        val rotatedBitmap = Bitmap.createBitmap(
            bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true
        )

        currentBitmap = rotatedBitmap
        statusText = "Đã xoay ảnh ${rotationAngle.toInt()}°"
        return true
    }

    /**
     * Lật ảnh theo chiều ngang (horizontal flip)
     * @return true nếu lật thành công, false nếu không có ảnh để lật
     */
    fun flipHorizontal(): Boolean {
        val bitmap = currentBitmap ?: return false

        val matrix = Matrix()
        matrix.preScale(-1.0f, 1.0f) // Lật theo chiều ngang

        val flippedBitmap = Bitmap.createBitmap(
            bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true
        )

        currentBitmap = flippedBitmap
        statusText = "Đã lật ảnh theo chiều ngang"
        return true
    }

    /**
     * Lật ảnh theo chiều dọc (vertical flip)
     * @return true nếu lật thành công, false nếu không có ảnh để lật
     */
    fun flipVertical(): Boolean {
        val bitmap = currentBitmap ?: return false

        val matrix = Matrix()
        matrix.preScale(1.0f, -1.0f) // Lật theo chiều dọc

        val flippedBitmap = Bitmap.createBitmap(
            bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true
        )

        currentBitmap = flippedBitmap
        statusText = "Đã lật ảnh theo chiều dọc"
        return true
    }
    
    /**
     * Reset trạng thái về ban đầu
     */
    fun reset() {
        currentBitmap = null
        rotationAngle = 0f
        statusText = "Chưa chọn ảnh nào"
    }
    
    /**
     * Kiểm tra xem có ảnh đang được load không
     */
    fun hasImage(): Boolean = currentBitmap != null
    
    /**
     * Lấy thông tin kích thước ảnh hiện tại
     */
    fun getImageInfo(): String? {
        return currentBitmap?.let { bitmap ->
            "Kích thước: ${bitmap.width} x ${bitmap.height} pixels"
        }
    }
    
    /**
     * Tạo bản sao của ảnh hiện tại
     */
    fun getCurrentBitmapCopy(): Bitmap? {
        return currentBitmap?.copy(currentBitmap!!.config, false)
    }

        // ==================== COLOR FILTERS ====================

        /**
        * Áp dụng ColorMatrix filter lên ảnh
        */
        private fun applyColorMatrix(colorMatrix: ColorMatrix, statusMessage: String): Boolean {
            val bitmap = currentBitmap ?: return false

            val result = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config)
            val canvas = Canvas(result)
            val paint = Paint()
            paint.colorFilter = ColorMatrixColorFilter(colorMatrix)
            canvas.drawBitmap(bitmap, 0f, 0f, paint)

            currentBitmap = result
            statusText = statusMessage
            return true
        }

        /**
        * Chuyển ảnh thành đen trắng (Grayscale)
        */
        fun applyGrayscale(): Boolean {
            val colorMatrix = ColorMatrix()
            colorMatrix.setSaturation(0f)
            return applyColorMatrix(colorMatrix, "Đã áp dụng bộ lọc đen trắng")
        }

        /**
        * Chuyển ảnh thành Sepia (màu nâu cổ điển)
        */
        fun applySepia(): Boolean {
            val colorMatrix = ColorMatrix()
            colorMatrix.set(floatArrayOf(
                0.393f, 0.769f, 0.189f, 0f, 0f,
                0.349f, 0.686f, 0.168f, 0f, 0f,
                0.272f, 0.534f, 0.131f, 0f, 0f,
                0f, 0f, 0f, 1f, 0f
            ))
            return applyColorMatrix(colorMatrix, "Đã áp dụng bộ lọc Sepia")
        }

        /**
        * Tăng độ sáng của ảnh
        */
        fun increaseBrightness(): Boolean {
            val colorMatrix = ColorMatrix()
            colorMatrix.set(floatArrayOf(
                1f, 0f, 0f, 0f, 30f,
                0f, 1f, 0f, 0f, 30f,
                0f, 0f, 1f, 0f, 30f,
                0f, 0f, 0f, 1f, 0f
            ))
            return applyColorMatrix(colorMatrix, "Đã tăng độ sáng")
        }

        /**
        * Giảm độ sáng của ảnh
        */
        fun decreaseBrightness(): Boolean {
            val colorMatrix = ColorMatrix()
            colorMatrix.set(floatArrayOf(
                1f, 0f, 0f, 0f, -30f,
                0f, 1f, 0f, 0f, -30f,
                0f, 0f, 1f, 0f, -30f,
                0f, 0f, 0f, 1f, 0f
            ))
            return applyColorMatrix(colorMatrix, "Đã giảm độ sáng")
        }

        /**
        * Tăng độ tương phản
        */
        fun increaseContrast(): Boolean {
            val contrast = 1.2f
            val translate = (-.5f * contrast + .5f) * 255f
            val colorMatrix = ColorMatrix()
            colorMatrix.set(floatArrayOf(
                contrast, 0f, 0f, 0f, translate,
                0f, contrast, 0f, 0f, translate,
                0f, 0f, contrast, 0f, translate,
                0f, 0f, 0f, 1f, 0f
            ))
            return applyColorMatrix(colorMatrix, "Đã tăng độ tương phản")
        }

        /**
        * Giảm độ tương phản
        */
        fun decreaseContrast(): Boolean {
            val contrast = 0.8f
            val translate = (-.5f * contrast + .5f) * 255f
            val colorMatrix = ColorMatrix()
            colorMatrix.set(floatArrayOf(
                contrast, 0f, 0f, 0f, translate,
                0f, contrast, 0f, 0f, translate,
                0f, 0f, contrast, 0f, translate,
                0f, 0f, 0f, 1f, 0f
            ))
            return applyColorMatrix(colorMatrix, "Đã giảm độ tương phản")
        }

        /**
        * Tăng độ bão hòa màu
        */
        fun increaseSaturation(): Boolean {
            val colorMatrix = ColorMatrix()
            colorMatrix.setSaturation(1.5f)
            return applyColorMatrix(colorMatrix, "Đã tăng độ bão hòa màu")
        }

        /**
        * Giảm độ bão hòa màu
        */
        fun decreaseSaturation(): Boolean {
            val colorMatrix = ColorMatrix()
            colorMatrix.setSaturation(0.5f)
            return applyColorMatrix(colorMatrix, "Đã giảm độ bão hòa màu")
        }

        /**
        * Bộ lọc màu xanh lạnh (Cool filter)
        */
        fun applyCoolFilter(): Boolean {
            val colorMatrix = ColorMatrix()
            colorMatrix.set(floatArrayOf(
                0.8f, 0f, 0f, 0f, 0f,
                0f, 0.9f, 0f, 0f, 0f,
                0f, 0f, 1.2f, 0f, 0f,
                0f, 0f, 0f, 1f, 0f
            ))
            return applyColorMatrix(colorMatrix, "Đã áp dụng bộ lọc màu lạnh")
        }

        /**
        * Bộ lọc màu ấm (Warm filter)
        */
        fun applyWarmFilter(): Boolean {
            val colorMatrix = ColorMatrix()
            colorMatrix.set(floatArrayOf(
                1.2f, 0f, 0f, 0f, 0f,
                0f, 1.1f, 0f, 0f, 0f,
                0f, 0f, 0.8f, 0f, 0f,
                0f, 0f, 0f, 1f, 0f
            ))
            return applyColorMatrix(colorMatrix, "Đã áp dụng bộ lọc màu ấm")
        }

        /**
        * Bộ lọc Vintage (màu cổ điển)
        */
        fun applyVintageFilter(): Boolean {
            val colorMatrix = ColorMatrix()
            colorMatrix.set(floatArrayOf(
                0.9f, 0.5f, 0.1f, 0f, 0f,
                0.3f, 0.8f, 0.1f, 0f, 0f,
                0.2f, 0.3f, 0.5f, 0f, 0f,
                0f, 0f, 0f, 1f, 0f
            ))
            return applyColorMatrix(colorMatrix, "Đã áp dụng bộ lọc Vintage")
        }

        /**
        * Đảo ngược màu (Invert colors)
        */
        fun invertColors(): Boolean {
            val colorMatrix = ColorMatrix()
            colorMatrix.set(floatArrayOf(
                -1f, 0f, 0f, 0f, 255f,
                0f, -1f, 0f, 0f, 255f,
                0f, 0f, -1f, 0f, 255f,
                0f, 0f, 0f, 1f, 0f
            ))
            return applyColorMatrix(colorMatrix, "Đã đảo ngược màu sắc")
        }
}
