package com.example.photofilter.processor

import android.content.ContentResolver
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.Matrix
import android.graphics.Paint
import android.net.Uri
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import java.io.IOException

class ImageProcessor {

    // State variables
    var currentBitmap by mutableStateOf<Bitmap?>(null)
        private set

    // Lưu trữ ảnh gốc để áp dụng bộ lọc
    private var originalBitmap: Bitmap? = null

    var rotationAngle by mutableStateOf(0f)
        private set

    // Slider value for rotation (0-360)
    var sliderRotationValue by mutableStateOf(0f)
        private set

    var isShowingOriginal by mutableStateOf(false)
        private set

    var keepOriginalSize by mutableStateOf(false)
        private set

    var useSmartCrop by mutableStateOf(false)
        private set

    var statusText by mutableStateOf("Chưa chọn ảnh nào")
        private set

    /**
     * Tải ảnh từ URI
     * @param uri URI của ảnh cần tải
     * @param contentResolver ContentResolver để đọc file
     * @return true nếu tải thành công, false nếu thất bại
     */
    fun loadImageFromUri(uri: Uri, contentResolver: ContentResolver): Boolean {
        return try {
            val inputStream = contentResolver.openInputStream(uri)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()

            if (bitmap != null) {
                // Lưu ảnh gốc và ảnh hiện tại
                originalBitmap = bitmap.config?.let { bitmap.copy(it, false) }
                currentBitmap = bitmap
                rotationAngle = 0f
                sliderRotationValue = 0f
                statusText = "Đã tải ảnh thành công"
                true
            } else {
                statusText = "Không thể tải ảnh"
                false
            }
        } catch (e: IOException) {
            statusText = "Lỗi khi tải ảnh: ${e.message}"
            false
        }
    }

    /**
     * Xoay ảnh hiện tại 90 độ
     * @return true nếu xoay thành công, false nếu không có ảnh để xoay
     */
    fun rotateImage(): Boolean {
        return rotateImageByAngle(90f)
    }

    /**
     * Xoay ảnh theo góc độ tùy chỉnh
     * @param angle Góc độ xoay (có thể âm hoặc dương)
     * @return true nếu xoay thành công, false nếu không có ảnh để xoay
     */
    fun rotateImageByAngle(angle: Float): Boolean {
        val bitmap = currentBitmap ?: return false

        rotationAngle += angle
        // Normalize angle to 0-360 range
        rotationAngle = ((rotationAngle % 360f) + 360f) % 360f

        val matrix = Matrix()
        matrix.postRotate(angle)

        val rotatedBitmap = Bitmap.createBitmap(
            bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true
        )

        // Cập nhật cả ảnh gốc và ảnh hiện tại khi xoay
        originalBitmap = rotatedBitmap.config?.let { rotatedBitmap.copy(it, false) }
        currentBitmap = rotatedBitmap
        statusText = "Đã xoay ảnh ${angle.toInt()}° (Tổng: ${rotationAngle.toInt()}°)"
        return true
    }

    /**
     * Xoay ảnh 45 độ
     */
    fun rotate45(): Boolean = rotateImageByAngle(45f)

    /**
     * Xoay ảnh -45 độ (ngược chiều kim đồng hồ)
     */
    fun rotateMinus45(): Boolean = rotateImageByAngle(-45f)

    /**
     * Xoay ảnh 180 độ
     */
    fun rotate180(): Boolean = rotateImageByAngle(180f)

    /**
     * Xoay ảnh -90 độ (ngược chiều kim đồng hồ)
     */
    fun rotateMinus90(): Boolean = rotateImageByAngle(-90f)

    /**
     * Xoay ảnh về góc độ cụ thể (từ ảnh gốc)
     * @param targetAngle Góc độ đích (0-360)
     */
    fun rotateToAngle(targetAngle: Float): Boolean {
        val bitmap = originalBitmap ?: return false

        val normalizedAngle = ((targetAngle % 360f) + 360f) % 360f
        rotationAngle = normalizedAngle
        sliderRotationValue = normalizedAngle

        val matrix = Matrix()
        matrix.postRotate(normalizedAngle)

        val rotatedBitmap = Bitmap.createBitmap(
            bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true
        )

        currentBitmap = rotatedBitmap
        statusText = "Đã xoay ảnh về ${normalizedAngle.toInt()}°"
        return true
    }

    /**
     * Xoay ảnh theo giá trị slider (0-360)
     * @param sliderValue Giá trị từ slider (0-360)
     * @param keepOriginalSize Giữ nguyên kích thước gốc hay không
     */
    fun rotateBySlider(sliderValue: Float, keepOriginalSize: Boolean = false): Boolean {
        val bitmap = originalBitmap ?: return false

        sliderRotationValue = sliderValue
        rotationAngle = sliderValue

        val rotatedBitmap = when {
            keepOriginalSize && useSmartCrop -> {
                // Giữ kích thước với crop thông minh
                rotateWithSmartCrop(bitmap, sliderValue)
            }
            keepOriginalSize -> {
                // Giữ kích thước với crop đơn giản
                rotateWithFixedSize(bitmap, sliderValue)
            }
            else -> {
                // Tự động điều chỉnh kích thước
                val matrix = Matrix()
                matrix.postRotate(sliderValue)
                Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
            }
        }

        currentBitmap = rotatedBitmap
        statusText = when {
            keepOriginalSize && useSmartCrop -> "Góc xoay: ${sliderValue.toInt()}° (Crop thông minh)"
            keepOriginalSize -> "Góc xoay: ${sliderValue.toInt()}° (Cho phép cắt)"
            else -> "Góc xoay: ${sliderValue.toInt()}°"
        }
        return true
    }

    /**
     * Xoay ảnh với kích thước cố định (cho phép cắt)
     */
    private fun rotateWithFixedSize(bitmap: Bitmap, angle: Float): Bitmap {
        val originalWidth = bitmap.width
        val originalHeight = bitmap.height

        // Tạo bitmap mới với kích thước gốc
        val rotatedBitmap = Bitmap.createBitmap(originalWidth, originalHeight, bitmap.config)
        val canvas = Canvas(rotatedBitmap)

        // Tô nền trắng để tránh vùng trong suốt
        canvas.drawColor(android.graphics.Color.WHITE)

        // Tính toán để căn giữa ảnh đã xoay
        val matrix = Matrix()

        // Xoay quanh tâm ảnh
        val centerX = originalWidth / 2f
        val centerY = originalHeight / 2f
        matrix.postRotate(angle, centerX, centerY)

        // Vẽ ảnh đã xoay lên canvas với khả năng bị cắt
        val paint = Paint(Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG)
        canvas.drawBitmap(bitmap, matrix, paint)

        return rotatedBitmap
    }

    /**
     * Xoay ảnh với crop thông minh (giữ phần trung tâm quan trọng)
     */
    private fun rotateWithSmartCrop(bitmap: Bitmap, angle: Float): Bitmap {
        val originalWidth = bitmap.width
        val originalHeight = bitmap.height

        // Tính toán kích thước vùng an toàn (không bị cắt) khi xoay
        val radians = Math.toRadians(angle.toDouble())
        val cosAngle = kotlin.math.abs(kotlin.math.cos(radians))
        val sinAngle = kotlin.math.abs(kotlin.math.sin(radians))

        // Kích thước vùng an toàn
        val safeWidth = (originalWidth * cosAngle + originalHeight * sinAngle).toInt()
        val safeHeight = (originalHeight * cosAngle + originalWidth * sinAngle).toInt()

        // Tạo bitmap tạm với kích thước đủ lớn
        val tempBitmap = Bitmap.createBitmap(safeWidth, safeHeight, bitmap.config)
        val tempCanvas = Canvas(tempBitmap)

        // Tô nền trắng
        tempCanvas.drawColor(android.graphics.Color.WHITE)

        // Xoay ảnh gốc
        val matrix = Matrix()
        matrix.postTranslate((safeWidth - originalWidth) / 2f, (safeHeight - originalHeight) / 2f)
        matrix.postRotate(angle, safeWidth / 2f, safeHeight / 2f)

        val paint = Paint(Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG)
        tempCanvas.drawBitmap(bitmap, matrix, paint)

        // Cắt về kích thước gốc từ trung tâm
        val cropX = (safeWidth - originalWidth) / 2
        val cropY = (safeHeight - originalHeight) / 2

        return Bitmap.createBitmap(tempBitmap, cropX, cropY, originalWidth, originalHeight)
    }

    /**
     * Toggle tùy chọn giữ kích thước gốc
     */
    fun toggleKeepOriginalSize() {
        keepOriginalSize = !keepOriginalSize
        // Áp dụng lại xoay với tùy chọn mới
        if (hasImage()) {
            rotateBySlider(sliderRotationValue, keepOriginalSize)
        }
    }

    /**
     * Toggle tùy chọn crop thông minh
     */
    fun toggleSmartCrop() {
        useSmartCrop = !useSmartCrop
        // Áp dụng lại xoay với tùy chọn mới
        if (hasImage() && keepOriginalSize) {
            rotateBySlider(sliderRotationValue, keepOriginalSize)
        }
    }

    /**
     * Lật ảnh theo chiều ngang (horizontal flip)
     * @return true nếu lật thành công, false nếu không có ảnh để lật
     */
    fun flipHorizontal(): Boolean {
        val bitmap = currentBitmap ?: return false

        val matrix = Matrix()
        matrix.preScale(-1.0f, 1.0f) // Lật theo chiều ngang

        val flippedBitmap = Bitmap.createBitmap(
            bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true
        )

        // Cập nhật cả ảnh gốc và ảnh hiện tại khi lật
        originalBitmap = flippedBitmap.config?.let { flippedBitmap.copy(it, false) }
        currentBitmap = flippedBitmap
        statusText = "Đã lật ảnh theo chiều ngang"
        return true
    }

    /**
     * Lật ảnh theo chiều dọc (vertical flip)
     * @return true nếu lật thành công, false nếu không có ảnh để lật
     */
    fun flipVertical(): Boolean {
        val bitmap = currentBitmap ?: return false

        val matrix = Matrix()
        matrix.preScale(1.0f, -1.0f) // Lật theo chiều dọc

        val flippedBitmap = Bitmap.createBitmap(
            bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true
        )

        // Cập nhật cả ảnh gốc và ảnh hiện tại khi lật
        originalBitmap = flippedBitmap.config?.let { flippedBitmap.copy(it, false) }
        currentBitmap = flippedBitmap
        statusText = "Đã lật ảnh theo chiều dọc"
        return true
    }

    /**
     * Reset trạng thái về ban đầu
     */
    fun reset() {
        currentBitmap = null
        originalBitmap = null
        initialOriginalBitmap = null
        rotationAngle = 0f
        sliderRotationValue = 0f
        isShowingOriginal = false
        keepOriginalSize = false
        useSmartCrop = false
        statusText = "Chưa chọn ảnh nào"
    }

    /**
     * Reset về ảnh gốc (không áp dụng bộ lọc)
     */
    fun resetToOriginal() {
        originalBitmap?.let { original ->
            currentBitmap = original.config?.let { original.copy(it, false) }
            statusText = "Đã reset về ảnh gốc"
        }
    }

    /**
     * Kiểm tra xem có ảnh đang được load không
     */
    fun hasImage(): Boolean = currentBitmap != null

    /**
     * Lấy thông tin kích thước ảnh hiện tại
     */
    fun getImageInfo(): String? {
        return currentBitmap?.let { bitmap ->
            "Kích thước: ${bitmap.width} x ${bitmap.height} pixels"
        }
    }

    /**
     * Tạo bản sao của ảnh hiện tại
     */
    fun getCurrentBitmapCopy(): Bitmap? {
        return currentBitmap!!.config?.let { currentBitmap?.copy(it, false) }
    }

    // ==================== COLOR FILTERS ====================

    /**
     * Áp dụng ColorMatrix filter lên ảnh GỐC (không tích lũy)
     */
    private fun applyColorMatrix(colorMatrix: ColorMatrix, statusMessage: String): Boolean {
        // Luôn áp dụng bộ lọc lên ảnh gốc để tránh tích lũy
        val bitmap = originalBitmap ?: return false

        val result = bitmap.config?.let { Bitmap.createBitmap(bitmap.width, bitmap.height, it) }
        val canvas = result?.let { Canvas(it) }
        val paint = Paint()
        paint.colorFilter = ColorMatrixColorFilter(colorMatrix)
        canvas?.drawBitmap(bitmap, 0f, 0f, paint)

        currentBitmap = result
        statusText = statusMessage
        return true
    }

    /**
     * Chuyển ảnh thành đen trắng (Grayscale)
     */
    fun applyGrayscale(): Boolean {
        val colorMatrix = ColorMatrix()
        colorMatrix.setSaturation(0f)
        return applyColorMatrix(colorMatrix, "Đã áp dụng bộ lọc đen trắng")
    }

    /**
     * Chuyển ảnh thành Sepia (màu nâu cổ điển)
     */
    fun applySepia(): Boolean {
        val colorMatrix = ColorMatrix()
        colorMatrix.set(floatArrayOf(
            0.393f, 0.769f, 0.189f, 0f, 0f,
            0.349f, 0.686f, 0.168f, 0f, 0f,
            0.272f, 0.534f, 0.131f, 0f, 0f,
            0f, 0f, 0f, 1f, 0f
        ))
        return applyColorMatrix(colorMatrix, "Đã áp dụng bộ lọc Sepia")
    }

    /**
     * Tăng độ sáng của ảnh
     */
    fun increaseBrightness(): Boolean {
        val colorMatrix = ColorMatrix()
        colorMatrix.set(floatArrayOf(
            1f, 0f, 0f, 0f, 30f,
            0f, 1f, 0f, 0f, 30f,
            0f, 0f, 1f, 0f, 30f,
            0f, 0f, 0f, 1f, 0f
        ))
        return applyColorMatrix(colorMatrix, "Đã tăng độ sáng")
    }

    /**
     * Giảm độ sáng của ảnh
     */
    fun decreaseBrightness(): Boolean {
        val colorMatrix = ColorMatrix()
        colorMatrix.set(floatArrayOf(
            1f, 0f, 0f, 0f, -30f,
            0f, 1f, 0f, 0f, -30f,
            0f, 0f, 1f, 0f, -30f,
            0f, 0f, 0f, 1f, 0f
        ))
        return applyColorMatrix(colorMatrix, "Đã giảm độ sáng")
    }

    /**
     * Tăng độ tương phản
     */
    fun increaseContrast(): Boolean {
        val contrast = 1.2f
        val translate = (-.5f * contrast + .5f) * 255f
        val colorMatrix = ColorMatrix()
        colorMatrix.set(floatArrayOf(
            contrast, 0f, 0f, 0f, translate,
            0f, contrast, 0f, 0f, translate,
            0f, 0f, contrast, 0f, translate,
            0f, 0f, 0f, 1f, 0f
        ))
        return applyColorMatrix(colorMatrix, "Đã tăng độ tương phản")
    }

    /**
     * Giảm độ tương phản
     */
    fun decreaseContrast(): Boolean {
        val contrast = 0.8f
        val translate = (-.5f * contrast + .5f) * 255f
        val colorMatrix = ColorMatrix()
        colorMatrix.set(floatArrayOf(
            contrast, 0f, 0f, 0f, translate,
            0f, contrast, 0f, 0f, translate,
            0f, 0f, contrast, 0f, translate,
            0f, 0f, 0f, 1f, 0f
        ))
        return applyColorMatrix(colorMatrix, "Đã giảm độ tương phản")
    }

    /**
     * Tăng độ bão hòa màu
     */
    fun increaseSaturation(): Boolean {
        val colorMatrix = ColorMatrix()
        colorMatrix.setSaturation(1.5f)
        return applyColorMatrix(colorMatrix, "Đã tăng độ bão hòa màu")
    }

    /**
     * Giảm độ bão hòa màu
     */
    fun decreaseSaturation(): Boolean {
        val colorMatrix = ColorMatrix()
        colorMatrix.setSaturation(0.5f)
        return applyColorMatrix(colorMatrix, "Đã giảm độ bão hòa màu")
    }

    /**
     * Bộ lọc màu xanh lạnh (Cool filter)
     */
    fun applyCoolFilter(): Boolean {
        val colorMatrix = ColorMatrix()
        colorMatrix.set(floatArrayOf(
            0.8f, 0f, 0f, 0f, 0f,
            0f, 0.9f, 0f, 0f, 0f,
            0f, 0f, 1.2f, 0f, 0f,
            0f, 0f, 0f, 1f, 0f
        ))
        return applyColorMatrix(colorMatrix, "Đã áp dụng bộ lọc màu lạnh")
    }

    /**
     * Bộ lọc màu ấm (Warm filter)
     */
    fun applyWarmFilter(): Boolean {
        val colorMatrix = ColorMatrix()
        colorMatrix.set(floatArrayOf(
            1.2f, 0f, 0f, 0f, 0f,
            0f, 1.1f, 0f, 0f, 0f,
            0f, 0f, 0.8f, 0f, 0f,
            0f, 0f, 0f, 1f, 0f
        ))
        return applyColorMatrix(colorMatrix, "Đã áp dụng bộ lọc màu ấm")
    }

    /**
     * Bộ lọc Vintage (màu cổ điển)
     */
    fun applyVintageFilter(): Boolean {
        val colorMatrix = ColorMatrix()
        colorMatrix.set(floatArrayOf(
            0.9f, 0.5f, 0.1f, 0f, 0f,
            0.3f, 0.8f, 0.1f, 0f, 0f,
            0.2f, 0.3f, 0.5f, 0f, 0f,
            0f, 0f, 0f, 1f, 0f
        ))
        return applyColorMatrix(colorMatrix, "Đã áp dụng bộ lọc Vintage")
    }

    /**
     * Đảo ngược màu (Invert colors)
     */
    fun invertColors(): Boolean {
        val colorMatrix = ColorMatrix()
        colorMatrix.set(floatArrayOf(
            -1f, 0f, 0f, 0f, 255f,
            0f, -1f, 0f, 0f, 255f,
            0f, 0f, -1f, 0f, 255f,
            0f, 0f, 0f, 1f, 0f
        ))
        return applyColorMatrix(colorMatrix, "Đã đảo ngược màu sắc")
    }
}
