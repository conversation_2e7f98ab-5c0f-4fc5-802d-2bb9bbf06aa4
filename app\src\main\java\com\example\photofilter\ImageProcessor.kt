package com.example.photofilter

import android.content.ContentResolver
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.net.Uri
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import java.io.IOException

class ImageProcessor {
    
    // State variables
    var currentBitmap by mutableStateOf<Bitmap?>(null)
        private set
    
    var rotationAngle by mutableStateOf(0f)
        private set
    
    var statusText by mutableStateOf("Chưa chọn ảnh nào")
        private set
    
    /**
     * Tải ảnh từ URI
     * @param uri URI của ảnh cần tải
     * @param contentResolver ContentResolver để đọc file
     * @return true nếu tải thành công, false nếu thất bại
     */
    fun loadImageFromUri(uri: Uri, contentResolver: ContentResolver): Boolean {
        return try {
            val inputStream = contentResolver.openInputStream(uri)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()
            
            if (bitmap != null) {
                currentBitmap = bitmap
                rotationAngle = 0f
                statusText = "Đã tải ảnh thành công"
                true
            } else {
                statusText = "Không thể tải ảnh"
                false
            }
        } catch (e: IOException) {
            statusText = "Lỗi khi tải ảnh: ${e.message}"
            false
        }
    }
    
    /**
     * Xoay ảnh hiện tại 90 độ
     * @return true nếu xoay thành công, false nếu không có ảnh để xoay
     */
    fun rotateImage(): Boolean {
        val bitmap = currentBitmap ?: return false

        rotationAngle += 90f
        if (rotationAngle >= 360f) {
            rotationAngle = 0f
        }

        val matrix = Matrix()
        matrix.postRotate(90f) // Luôn xoay 90 độ mỗi lần

        val rotatedBitmap = Bitmap.createBitmap(
            bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true
        )

        currentBitmap = rotatedBitmap
        statusText = "Đã xoay ảnh ${rotationAngle.toInt()}°"
        return true
    }

    /**
     * Lật ảnh theo chiều ngang (horizontal flip)
     * @return true nếu lật thành công, false nếu không có ảnh để lật
     */
    fun flipHorizontal(): Boolean {
        val bitmap = currentBitmap ?: return false

        val matrix = Matrix()
        matrix.preScale(-1.0f, 1.0f) // Lật theo chiều ngang

        val flippedBitmap = Bitmap.createBitmap(
            bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true
        )

        currentBitmap = flippedBitmap
        statusText = "Đã lật ảnh theo chiều ngang"
        return true
    }

    /**
     * Lật ảnh theo chiều dọc (vertical flip)
     * @return true nếu lật thành công, false nếu không có ảnh để lật
     */
    fun flipVertical(): Boolean {
        val bitmap = currentBitmap ?: return false

        val matrix = Matrix()
        matrix.preScale(1.0f, -1.0f) // Lật theo chiều dọc

        val flippedBitmap = Bitmap.createBitmap(
            bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true
        )

        currentBitmap = flippedBitmap
        statusText = "Đã lật ảnh theo chiều dọc"
        return true
    }
    
    /**
     * Reset trạng thái về ban đầu
     */
    fun reset() {
        currentBitmap = null
        rotationAngle = 0f
        statusText = "Chưa chọn ảnh nào"
    }
    
    /**
     * Kiểm tra xem có ảnh đang được load không
     */
    fun hasImage(): Boolean = currentBitmap != null
    
    /**
     * Lấy thông tin kích thước ảnh hiện tại
     */
    fun getImageInfo(): String? {
        return currentBitmap?.let { bitmap ->
            "Kích thước: ${bitmap.width} x ${bitmap.height} pixels"
        }
    }
    
    /**
     * Tạo bản sao của ảnh hiện tại
     */
    fun getCurrentBitmapCopy(): Bitmap? {
        return currentBitmap?.copy(currentBitmap!!.config, false)
    }
}
