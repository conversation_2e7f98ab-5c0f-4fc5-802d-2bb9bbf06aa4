<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".MainActivity">

    <!-- Ti<PERSON>u đề -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Ứng dụng Tải và Xoay Ảnh"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="20dp"
        android:textColor="@color/purple_700" />

    <!-- <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> đ<PERSON>n -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginBottom="20dp">

        <Button
            android:id="@+id/btnSelectImage"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/select_image"
            android:layout_marginEnd="8dp"
            android:backgroundTint="@color/purple_500" />

        <Button
            android:id="@+id/btnRotateImage"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/rotate_image"
            android:layout_marginStart="8dp"
            android:backgroundTint="@color/teal_700"
            android:enabled="false" />

    </LinearLayout>

    <!-- Khu vực hiển thị ảnh -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <ImageView
                android:id="@+id/imageView"
                android:layout_width="match_parent"
                android:layout_height="400dp"
                android:scaleType="centerInside"
                android:background="@color/teal_200"
                android:contentDescription="Selected image"
                android:layout_margin="8dp" />

            <TextView
                android:id="@+id/tvImageStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/no_image_selected"
                android:textSize="16sp"
                android:textColor="@color/purple_700"
                android:layout_marginTop="16dp" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>
