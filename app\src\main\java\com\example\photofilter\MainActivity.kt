package com.example.photofilter

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.provider.MediaStore
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.ui.Modifier
import androidx.core.content.ContextCompat
import com.example.myapp.ui.theme.PhotoFilterTheme
import com.example.photofilter.processor.ImageProcessor

class MainActivity : ComponentActivity() {

    // ImageProcessor instance để xử lý ảnh
    private lateinit var imageProcessor: ImageProcessor

    // Activity result launcher để chọn ảnh từ gallery
    private val selectImageLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            result.data?.data?.let { uri ->
                val success = imageProcessor.loadImageFromUri(uri, contentResolver)
                if (!success) {
                    Toast.makeText(this, imageProcessor.statusText, Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    // Permission launcher để xin quyền truy cập storage
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            openImagePicker()
        } else {
            Toast.makeText(this, "Cần quyền truy cập để chọn ảnh", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Khởi tạo ImageProcessor
        imageProcessor = ImageProcessor()

        setContent {
            PhotoFilterTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colors.background
                ) {
                    ImageRotatorScreen(
                        imageProcessor = imageProcessor,
                        onSelectImageClick = { checkPermissionAndSelectImage() },
                        onRotationSliderChange = { value -> handleRotationSliderChange(value) },
                        onToggleKeepSizeClick = { handleToggleKeepSize() },
                        onFlipHorizontalClick = { handleFlipHorizontal() },
                        onFlipVerticalClick = { handleFlipVertical() },
                        onResetClick = { handleReset() },
                        // Color filter handlers
                        onGrayscaleClick = { handleColorFilter { imageProcessor.applyGrayscale() } },
                        onSepiaClick = { handleColorFilter { imageProcessor.applySepia() } },
                        onIncreaseBrightnessClick = { handleColorFilter { imageProcessor.increaseBrightness() } },
                        onDecreaseBrightnessClick = { handleColorFilter { imageProcessor.decreaseBrightness() } },
                        onIncreaseContrastClick = { handleColorFilter { imageProcessor.increaseContrast() } },
                        onDecreaseContrastClick = { handleColorFilter { imageProcessor.decreaseContrast() } },
                        onIncreaseSaturationClick = { handleColorFilter { imageProcessor.increaseSaturation() } },
                        onDecreaseSaturationClick = { handleColorFilter { imageProcessor.decreaseSaturation() } },
                        onCoolFilterClick = { handleColorFilter { imageProcessor.applyCoolFilter() } },
                        onWarmFilterClick = { handleColorFilter { imageProcessor.applyWarmFilter() } },
                        onVintageFilterClick = { handleColorFilter { imageProcessor.applyVintageFilter() } },
                        onInvertColorsClick = { handleColorFilter { imageProcessor.invertColors() } }
                    )
                }
            }
        }
    }

    /**
     * Kiểm tra quyền và chọn ảnh
     */
    private fun checkPermissionAndSelectImage() {
        when {
            ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED -> {
                openImagePicker()
            }
            else -> {
                requestPermissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
        }
    }

    /**
     * Mở image picker
     */
    private fun openImagePicker() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        intent.type = "image/*"
        selectImageLauncher.launch(intent)
    }

    /**
     * Xử lý thay đổi slider xoay ảnh
     */
    private fun handleRotationSliderChange(value: Float) {
        val success = imageProcessor.rotateBySlider(value, imageProcessor.keepOriginalSize)
        if (!success) {
            Toast.makeText(this, "Không có ảnh để xoay", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Xử lý toggle giữ kích thước gốc
     */
    private fun handleToggleKeepSize() {
        imageProcessor.toggleKeepOriginalSize()
    }

    /**
     * Xử lý lật ảnh theo chiều ngang
     */
    private fun handleFlipHorizontal() {
        val success = imageProcessor.flipHorizontal()
        if (!success) {
            Toast.makeText(this, "Không có ảnh để lật", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Xử lý lật ảnh theo chiều dọc
     */
    private fun handleFlipVertical() {
        val success = imageProcessor.flipVertical()
        if (!success) {
            Toast.makeText(this, "Không có ảnh để lật", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Xử lý các bộ lọc màu
     */
    private fun handleColorFilter(filterAction: () -> Boolean) {
        val success = filterAction()
        if (!success) {
            Toast.makeText(this, "Không có ảnh để áp dụng bộ lọc", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Xử lý reset ảnh
     */
    private fun handleReset() {
        imageProcessor.reset()
        Toast.makeText(this, "Đã reset ảnh", Toast.LENGTH_SHORT).show()
    }
}
